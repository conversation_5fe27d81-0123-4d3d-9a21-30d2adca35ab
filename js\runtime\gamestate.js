/**
 * 游戏状态管理器
 * 负责管理游戏的各种状态
 */
export default class GameState {
  constructor() {
    this.states = {
      MENU: 'menu',
      PLAYING: 'playing',
      PAUSED: 'paused',
      GAME_OVER: 'gameOver',
      LEVEL_COMPLETE: 'levelComplete',
      LOADING: 'loading'
    };
    
    this.currentState = this.states.MENU;
    this.previousState = null;
    this.stateTimer = 0;
    this.callbacks = {};
  }

  /**
   * 设置游戏状态
   * @param {string} newState - 新状态
   */
  setState(newState) {
    if (this.states[newState] || Object.values(this.states).includes(newState)) {
      this.previousState = this.currentState;
      this.currentState = newState;
      this.stateTimer = 0;
      
      // 触发状态变化回调
      if (this.callbacks[newState]) {
        this.callbacks[newState].forEach(callback => callback());
      }
    }
  }

  /**
   * 获取当前状态
   * @returns {string} 当前状态
   */
  getState() {
    return this.currentState;
  }

  /**
   * 检查是否为指定状态
   * @param {string} state - 要检查的状态
   * @returns {boolean} 是否为指定状态
   */
  isState(state) {
    return this.currentState === state;
  }

  /**
   * 切换暂停状态
   */
  togglePause() {
    if (this.currentState === this.states.PLAYING) {
      this.setState(this.states.PAUSED);
    } else if (this.currentState === this.states.PAUSED) {
      this.setState(this.states.PLAYING);
    }
  }

  /**
   * 注册状态变化回调
   * @param {string} state - 状态
   * @param {function} callback - 回调函数
   */
  onStateChange(state, callback) {
    if (!this.callbacks[state]) {
      this.callbacks[state] = [];
    }
    this.callbacks[state].push(callback);
  }

  /**
   * 更新状态计时器
   */
  update() {
    this.stateTimer++;
  }

  /**
   * 获取状态持续时间
   * @returns {number} 当前状态持续的帧数
   */
  getStateTime() {
    return this.stateTimer;
  }

  /**
   * 重置状态管理器
   */
  reset() {
    this.currentState = this.states.MENU;
    this.previousState = null;
    this.stateTimer = 0;
  }

  /**
   * 渲染状态相关的UI
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderStateUI(ctx) {
    ctx.save();
    
    switch (this.currentState) {
      case this.states.PAUSED:
        this.renderPauseScreen(ctx);
        break;
      case this.states.LEVEL_COMPLETE:
        this.renderLevelCompleteScreen(ctx);
        break;
      case this.states.LOADING:
        this.renderLoadingScreen(ctx);
        break;
    }
    
    ctx.restore();
  }

  /**
   * 渲染暂停屏幕
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderPauseScreen(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 暂停文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 48px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('PAUSED', canvas.width / 2, canvas.height / 2 - 50);
    
    // 提示文字
    ctx.font = '24px Arial';
    ctx.fillText('Touch to Resume', canvas.width / 2, canvas.height / 2 + 20);
  }

  /**
   * 渲染关卡完成屏幕
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderLevelCompleteScreen(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 100, 0, 0.8)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 完成文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 36px Arial';
    ctx.textAlign = 'center';
    
    const text = GameGlobal.databus.isBossLevel ? 'BOSS DEFEATED!' : 'LEVEL COMPLETE!';
    ctx.fillText(text, canvas.width / 2, canvas.height / 2 - 30);
    
    // 分数信息
    ctx.font = '24px Arial';
    ctx.fillText(`Score: ${GameGlobal.databus.score}`, canvas.width / 2, canvas.height / 2 + 20);
    ctx.fillText(`Next Level: ${GameGlobal.databus.level}`, canvas.width / 2, canvas.height / 2 + 50);
  }

  /**
   * 渲染加载屏幕
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderLoadingScreen(ctx) {
    // 背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 加载文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Loading...', canvas.width / 2, canvas.height / 2);
    
    // 加载动画（简单的旋转点）
    const dots = Math.floor(this.stateTimer / 20) % 4;
    ctx.fillText('.'.repeat(dots), canvas.width / 2 + 100, canvas.height / 2);
  }
}

// 创建全局游戏状态管理器
GameGlobal.gameState = new GameState();
