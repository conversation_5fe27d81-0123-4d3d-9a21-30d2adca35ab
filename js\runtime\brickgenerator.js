import Brick, { BRICK_TYPES } from '../npc/brick';
import { SCREEN_WIDTH } from '../render';

const BRICK_WIDTH = 60;
const BRICK_HEIGHT = 30;
const BRICK_MARGIN = 2;
const BRICKS_PER_ROW = Math.floor(SCREEN_WIDTH / (BRICK_WIDTH + BRICK_MARGIN));

/**
 * 砖块生成器类
 * 负责生成类似俄罗斯方块的砖块模式
 */
export default class BrickGenerator {
  constructor() {
    this.generateTimer = 0;
    this.generateInterval = 180; // 3秒生成一行砖块
    this.patterns = this.initPatterns();
  }

  /**
   * 初始化砖块生成模式
   */
  initPatterns() {
    return {
      // 普通关卡模式
      normal: [
        // 模式1：完整一行
        [1, 1, 1, 1, 1, 1, 1, 1],
        // 模式2：中间空缺
        [1, 1, 1, 0, 0, 1, 1, 1],
        // 模式3：两边空缺
        [0, 1, 1, 1, 1, 1, 1, 0],
        // 模式4：间隔模式
        [1, 0, 1, 0, 1, 0, 1, 0],
        // 模式5：随机模式
        [1, 1, 0, 1, 0, 1, 1, 0]
      ],
      // Boss关卡模式
      boss: [
        // Boss模式：更密集的砖块
        [1, 1, 1, 1, 1, 1, 1, 1],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [0, 1, 1, 1, 1, 1, 1, 0]
      ]
    };
  }

  /**
   * 更新砖块生成器
   */
  update() {
    if (GameGlobal.databus.isGameOver) {
      return;
    }

    this.generateTimer++;
    
    // 根据关卡调整生成频率
    const level = GameGlobal.databus.level;
    const adjustedInterval = Math.max(90, this.generateInterval - level * 8);

    // 只有当砖块数量不太多时才生成新的
    const maxBricks = 50 + level * 5;

    if (this.generateTimer >= adjustedInterval && GameGlobal.databus.bricks.length < maxBricks) {
      this.generateBrickRow();
      this.generateTimer = 0;
    }
  }

  /**
   * 生成一行砖块
   */
  generateBrickRow() {
    const isBossLevel = GameGlobal.databus.isBossLevel;
    const patterns = isBossLevel ? this.patterns.boss : this.patterns.normal;
    const pattern = patterns[Math.floor(Math.random() * patterns.length)];

    const startY = -BRICK_HEIGHT; // 从屏幕顶部上方开始
    let currentX = BRICK_MARGIN; // 当前X位置

    for (let i = 0; i < Math.min(pattern.length, BRICKS_PER_ROW); i++) {
      if (pattern[i] === 1) {
        // 检查是否有足够空间放置砖块
        if (currentX + BRICK_WIDTH > SCREEN_WIDTH) {
          break; // 超出屏幕宽度，停止生成
        }

        const brick = GameGlobal.databus.pool.getItemByClass('brick', Brick);

        // 根据关卡和是否为Boss关卡决定砖块生命值
        let health;
        if (isBossLevel) {
          health = Math.floor(Math.random() * 4) + 3; // Boss关卡：3-6生命值
        } else {
          health = Math.floor(Math.random() * 4) + 2; // 普通关卡：2-5生命值
        }

        const type = isBossLevel ? BRICK_TYPES.BOSS : BRICK_TYPES.NORMAL;

        brick.init(currentX, startY, health, type);

        // 检查是否与现有砖块重叠
        if (!this.checkBrickOverlap(brick)) {
          GameGlobal.databus.bricks.push(brick);
          currentX += brick.width + BRICK_MARGIN; // 根据实际砖块宽度更新位置
        } else {
          // 如果重叠，回收砖块
          GameGlobal.databus.pool.recover('brick', brick);
        }
      } else {
        // 空位置，跳过一个砖块宽度
        currentX += BRICK_WIDTH + BRICK_MARGIN;
      }
    }
  }

  /**
   * 检查砖块是否与现有砖块重叠
   */
  checkBrickOverlap(newBrick) {
    const existingBricks = GameGlobal.databus.bricks;

    for (let brick of existingBricks) {
      if (brick.isActive && brick.visible) {
        // 检查AABB碰撞
        if (newBrick.x < brick.x + brick.width &&
            newBrick.x + newBrick.width > brick.x &&
            newBrick.y < brick.y + brick.height &&
            newBrick.y + newBrick.height > brick.y) {
          return true; // 发生重叠
        }
      }
    }
    return false; // 没有重叠
  }

  /**
   * 生成初始砖块（游戏开始时）
   */
  generateInitialBricks() {
    const level = GameGlobal.databus.level;
    const isBossLevel = GameGlobal.databus.isBossLevel;

    // 根据关卡生成初始砖块行数
    const initialRows = Math.min(3 + Math.floor(level / 2), 8);

    for (let row = 0; row < initialRows; row++) {
      const patterns = isBossLevel ? this.patterns.boss : this.patterns.normal;
      const pattern = patterns[Math.floor(Math.random() * patterns.length)];

      const y = row * (BRICK_HEIGHT * 1.2 + BRICK_MARGIN) + 50; // 增加行间距避免重叠
      let currentX = BRICK_MARGIN; // 当前X位置

      for (let i = 0; i < Math.min(pattern.length, BRICKS_PER_ROW); i++) {
        if (pattern[i] === 1) {
          // 检查是否有足够空间
          if (currentX + BRICK_WIDTH > SCREEN_WIDTH) {
            break;
          }

          const brick = GameGlobal.databus.pool.getItemByClass('brick', Brick);

          let health;
          if (isBossLevel) {
            health = Math.floor(Math.random() * 4) + 3; // Boss关卡：3-6生命值
          } else {
            health = Math.floor(Math.random() * 4) + 2; // 普通关卡：2-5生命值
          }

          const type = isBossLevel ? BRICK_TYPES.BOSS : BRICK_TYPES.NORMAL;

          brick.init(currentX, y, health, type);

          // 检查重叠
          if (!this.checkBrickOverlap(brick)) {
            GameGlobal.databus.bricks.push(brick);
            currentX += brick.width + BRICK_MARGIN;
          } else {
            GameGlobal.databus.pool.recover('brick', brick);
          }
        } else {
          currentX += BRICK_WIDTH + BRICK_MARGIN;
        }
      }
    }
  }

  /**
   * 重置生成器
   */
  reset() {
    this.generateTimer = 0;
  }

  /**
   * 生成特殊模式砖块（用于测试或特殊关卡）
   */
  generateSpecialPattern(patternName) {
    const specialPatterns = {
      // 心形模式
      heart: [
        [0, 1, 1, 0, 0, 1, 1, 0],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [0, 1, 1, 1, 1, 1, 1, 0],
        [0, 0, 1, 1, 1, 1, 0, 0],
        [0, 0, 0, 1, 1, 0, 0, 0]
      ],
      // 钻石模式
      diamond: [
        [0, 0, 0, 1, 1, 0, 0, 0],
        [0, 0, 1, 1, 1, 1, 0, 0],
        [0, 1, 1, 1, 1, 1, 1, 0],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [0, 1, 1, 1, 1, 1, 1, 0],
        [0, 0, 1, 1, 1, 1, 0, 0],
        [0, 0, 0, 1, 1, 0, 0, 0]
      ]
    };

    const pattern = specialPatterns[patternName];
    if (!pattern) return;

    const startY = 50;
    
    for (let row = 0; row < pattern.length; row++) {
      const y = startY + row * (BRICK_HEIGHT + BRICK_MARGIN);
      
      for (let i = 0; i < Math.min(pattern[row].length, BRICKS_PER_ROW); i++) {
        if (pattern[row][i] === 1) {
          const brick = GameGlobal.databus.pool.getItemByClass('brick', Brick);
          const x = i * (BRICK_WIDTH + BRICK_MARGIN) + BRICK_MARGIN;
          
          const health = Math.floor(Math.random() * 4) + 2;
          const type = BRICK_TYPES.NORMAL;
          
          brick.init(x, y, health, type);
          GameGlobal.databus.bricks.push(brick);
        }
      }
    }
  }
}
