import Brick, { BRICK_TYPES } from '../npc/brick';
import { SCREEN_WIDTH } from '../render';

const BRICK_WIDTH = 60;
const BRICK_HEIGHT = 30;
const BRICK_MARGIN = 2;
const BRICKS_PER_ROW = Math.floor(SCREEN_WIDTH / (BRICK_WIDTH + BRICK_MARGIN));

/**
 * 砖块生成器类
 * 负责生成类似俄罗斯方块的砖块模式
 */
export default class BrickGenerator {
  constructor() {
    this.generateTimer = 0;
    this.generateInterval = 180; // 3秒生成一行砖块
    this.patterns = this.initPatterns();
  }

  /**
   * 初始化砖块生成模式
   */
  initPatterns() {
    return {
      // 普通关卡模式
      normal: [
        // 模式1：完整一行
        [1, 1, 1, 1, 1, 1, 1, 1],
        // 模式2：中间空缺
        [1, 1, 1, 0, 0, 1, 1, 1],
        // 模式3：两边空缺
        [0, 1, 1, 1, 1, 1, 1, 0],
        // 模式4：间隔模式
        [1, 0, 1, 0, 1, 0, 1, 0],
        // 模式5：随机模式
        [1, 1, 0, 1, 0, 1, 1, 0]
      ],
      // Boss关卡模式
      boss: [
        // Boss模式：更密集的砖块
        [1, 1, 1, 1, 1, 1, 1, 1],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [0, 1, 1, 1, 1, 1, 1, 0]
      ]
    };
  }

  /**
   * 更新砖块生成器
   */
  update() {
    if (GameGlobal.databus.isGameOver) {
      return;
    }

    this.generateTimer++;
    
    // 根据关卡调整生成频率
    const level = GameGlobal.databus.level;
    const adjustedInterval = Math.max(60, this.generateInterval - level * 5);
    
    if (this.generateTimer >= adjustedInterval) {
      this.generateBrickRow();
      this.generateTimer = 0;
    }
  }

  /**
   * 生成一行砖块
   */
  generateBrickRow() {
    const isBossLevel = GameGlobal.databus.isBossLevel;
    const patterns = isBossLevel ? this.patterns.boss : this.patterns.normal;
    const pattern = patterns[Math.floor(Math.random() * patterns.length)];
    
    const startY = -BRICK_HEIGHT; // 从屏幕顶部上方开始
    
    for (let i = 0; i < Math.min(pattern.length, BRICKS_PER_ROW); i++) {
      if (pattern[i] === 1) {
        const brick = GameGlobal.databus.pool.getItemByClass('brick', Brick);
        const x = i * (BRICK_WIDTH + BRICK_MARGIN) + BRICK_MARGIN;
        
        // 根据关卡和是否为Boss关卡决定砖块生命值
        let health;
        if (isBossLevel) {
          health = Math.floor(Math.random() * 4) + 3; // Boss关卡：3-6生命值
        } else {
          health = Math.floor(Math.random() * 4) + 2; // 普通关卡：2-5生命值
        }
        
        const type = isBossLevel ? BRICK_TYPES.BOSS : BRICK_TYPES.NORMAL;
        
        brick.init(x, startY, health, type);
        GameGlobal.databus.bricks.push(brick);
      }
    }
  }

  /**
   * 生成初始砖块（游戏开始时）
   */
  generateInitialBricks() {
    const level = GameGlobal.databus.level;
    const isBossLevel = GameGlobal.databus.isBossLevel;
    
    // 根据关卡生成初始砖块行数
    const initialRows = Math.min(3 + Math.floor(level / 2), 8);
    
    for (let row = 0; row < initialRows; row++) {
      const patterns = isBossLevel ? this.patterns.boss : this.patterns.normal;
      const pattern = patterns[Math.floor(Math.random() * patterns.length)];
      
      const y = row * (BRICK_HEIGHT + BRICK_MARGIN) + 50; // 从屏幕顶部开始，留出一些空间
      
      for (let i = 0; i < Math.min(pattern.length, BRICKS_PER_ROW); i++) {
        if (pattern[i] === 1) {
          const brick = GameGlobal.databus.pool.getItemByClass('brick', Brick);
          const x = i * (BRICK_WIDTH + BRICK_MARGIN) + BRICK_MARGIN;
          
          let health;
          if (isBossLevel) {
            health = Math.floor(Math.random() * 4) + 3; // Boss关卡：3-6生命值
          } else {
            health = Math.floor(Math.random() * 4) + 2; // 普通关卡：2-5生命值
          }
          
          const type = isBossLevel ? BRICK_TYPES.BOSS : BRICK_TYPES.NORMAL;
          
          brick.init(x, y, health, type);
          GameGlobal.databus.bricks.push(brick);
        }
      }
    }
  }

  /**
   * 重置生成器
   */
  reset() {
    this.generateTimer = 0;
  }

  /**
   * 生成特殊模式砖块（用于测试或特殊关卡）
   */
  generateSpecialPattern(patternName) {
    const specialPatterns = {
      // 心形模式
      heart: [
        [0, 1, 1, 0, 0, 1, 1, 0],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [0, 1, 1, 1, 1, 1, 1, 0],
        [0, 0, 1, 1, 1, 1, 0, 0],
        [0, 0, 0, 1, 1, 0, 0, 0]
      ],
      // 钻石模式
      diamond: [
        [0, 0, 0, 1, 1, 0, 0, 0],
        [0, 0, 1, 1, 1, 1, 0, 0],
        [0, 1, 1, 1, 1, 1, 1, 0],
        [1, 1, 1, 1, 1, 1, 1, 1],
        [0, 1, 1, 1, 1, 1, 1, 0],
        [0, 0, 1, 1, 1, 1, 0, 0],
        [0, 0, 0, 1, 1, 0, 0, 0]
      ]
    };

    const pattern = specialPatterns[patternName];
    if (!pattern) return;

    const startY = 50;
    
    for (let row = 0; row < pattern.length; row++) {
      const y = startY + row * (BRICK_HEIGHT + BRICK_MARGIN);
      
      for (let i = 0; i < Math.min(pattern[row].length, BRICKS_PER_ROW); i++) {
        if (pattern[row][i] === 1) {
          const brick = GameGlobal.databus.pool.getItemByClass('brick', Brick);
          const x = i * (BRICK_WIDTH + BRICK_MARGIN) + BRICK_MARGIN;
          
          const health = Math.floor(Math.random() * 4) + 2;
          const type = BRICK_TYPES.NORMAL;
          
          brick.init(x, y, health, type);
          GameGlobal.databus.bricks.push(brick);
        }
      }
    }
  }
}
