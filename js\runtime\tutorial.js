/**
 * 游戏教程和说明系统
 */
export default class Tutorial {
  constructor() {
    this.isShowing = false;
    this.currentStep = 0;
    this.steps = [
      {
        title: "欢迎来到弹球游戏！",
        content: "这是一个类似俄罗斯方块的弹球游戏",
        duration: 180 // 3秒
      },
      {
        title: "游戏目标",
        content: "用球击破所有砖块，不让砖块到达底部",
        duration: 180
      },
      {
        title: "操作方法",
        content: "触摸屏幕调整发射角度，松开发射球",
        duration: 180
      },
      {
        title: "砖块系统",
        content: "砖块有2-5生命值，颜色表示生命值高低",
        duration: 180
      },
      {
        title: "关卡系统",
        content: "每5关有一个Boss关卡，难度会逐渐增加",
        duration: 180
      },
      {
        title: "开始游戏！",
        content: "触摸屏幕开始你的弹球之旅",
        duration: 120
      }
    ];
    this.stepTimer = 0;
    this.showOnStart = true;
  }

  /**
   * 开始显示教程
   */
  start() {
    if (this.showOnStart) {
      this.isShowing = true;
      this.currentStep = 0;
      this.stepTimer = 0;
    }
  }

  /**
   * 跳过教程
   */
  skip() {
    this.isShowing = false;
    this.showOnStart = false;
  }

  /**
   * 更新教程
   */
  update() {
    if (!this.isShowing) return;

    this.stepTimer++;
    
    // 自动进入下一步
    if (this.stepTimer >= this.steps[this.currentStep].duration) {
      this.nextStep();
    }
  }

  /**
   * 下一步
   */
  nextStep() {
    this.currentStep++;
    this.stepTimer = 0;
    
    if (this.currentStep >= this.steps.length) {
      this.isShowing = false;
      this.showOnStart = false;
    }
  }

  /**
   * 渲染教程
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (!this.isShowing) return;

    const step = this.steps[this.currentStep];
    if (!step) return;

    ctx.save();
    
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 教程框背景
    const boxWidth = canvas.width * 0.8;
    const boxHeight = 200;
    const boxX = (canvas.width - boxWidth) / 2;
    const boxY = (canvas.height - boxHeight) / 2;
    
    ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
    ctx.fillRect(boxX, boxY, boxWidth, boxHeight);
    
    // 边框
    ctx.strokeStyle = '#4ECDC4';
    ctx.lineWidth = 3;
    ctx.strokeRect(boxX, boxY, boxWidth, boxHeight);
    
    // 标题
    ctx.fillStyle = '#2C3E50';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(step.title, canvas.width / 2, boxY + 40);
    
    // 内容
    ctx.font = '18px Arial';
    ctx.fillStyle = '#34495E';
    this.wrapText(ctx, step.content, canvas.width / 2, boxY + 80, boxWidth - 40, 25);
    
    // 进度指示器
    const progressWidth = boxWidth - 40;
    const progressHeight = 4;
    const progressX = boxX + 20;
    const progressY = boxY + boxHeight - 30;
    
    // 进度条背景
    ctx.fillStyle = '#BDC3C7';
    ctx.fillRect(progressX, progressY, progressWidth, progressHeight);
    
    // 进度条
    const progress = this.stepTimer / step.duration;
    ctx.fillStyle = '#4ECDC4';
    ctx.fillRect(progressX, progressY, progressWidth * progress, progressHeight);
    
    // 步骤指示器
    ctx.fillStyle = '#7F8C8D';
    ctx.font = '14px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(`${this.currentStep + 1}/${this.steps.length}`, boxX + boxWidth - 20, boxY + boxHeight - 10);
    
    // 跳过按钮
    ctx.fillStyle = '#E74C3C';
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('触摸跳过', boxX + 20, boxY + boxHeight - 10);
    
    ctx.restore();
  }

  /**
   * 文字换行显示
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {string} text - 文字内容
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} maxWidth - 最大宽度
   * @param {number} lineHeight - 行高
   */
  wrapText(ctx, text, x, y, maxWidth, lineHeight) {
    const words = text.split('');
    let line = '';
    let currentY = y;

    for (let i = 0; i < words.length; i++) {
      const testLine = line + words[i];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && i > 0) {
        ctx.fillText(line, x, currentY);
        line = words[i];
        currentY += lineHeight;
      } else {
        line = testLine;
      }
    }
    ctx.fillText(line, x, currentY);
  }

  /**
   * 处理触摸事件
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   * @returns {boolean} 是否处理了事件
   */
  handleTouch(x, y) {
    if (!this.isShowing) return false;

    // 检查是否点击了跳过区域
    const boxWidth = canvas.width * 0.8;
    const boxHeight = 200;
    const boxX = (canvas.width - boxWidth) / 2;
    const boxY = (canvas.height - boxHeight) / 2;
    
    if (x >= boxX && x <= boxX + 100 && y >= boxY + boxHeight - 30 && y <= boxY + boxHeight) {
      this.skip();
      return true;
    }

    // 其他区域点击进入下一步
    this.nextStep();
    return true;
  }

  /**
   * 重置教程
   */
  reset() {
    this.isShowing = false;
    this.currentStep = 0;
    this.stepTimer = 0;
    this.showOnStart = true;
  }
}

// 创建全局教程实例
GameGlobal.tutorial = new Tutorial();
