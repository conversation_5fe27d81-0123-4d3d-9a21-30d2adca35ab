import Animation from '../base/animation';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render';

const BRICK_IMG_SRC = 'images/enemy.png'; // 暂时使用敌机图片作为砖块
const BRICK_WIDTH = 60;
const BRICK_HEIGHT = 30;
const EXPLO_IMG_PREFIX = 'images/explosion';

// 砖块类型
const BRICK_TYPES = {
  NORMAL: 'normal',
  BOSS: 'boss',
  SPECIAL: 'special' // 特效砖块
};

// 砖块形状
const BRICK_SHAPES = {
  SQUARE: 'square',
  RECTANGLE: 'rectangle'
};

// 砖块颜色配置（根据生命值）
const BRICK_COLORS = {
  2: '#FF6B6B', // 红色 - 2生命值
  3: '#4ECDC4', // 青色 - 3生命值
  4: '#45B7D1', // 蓝色 - 4生命值
  5: '#96CEB4', // 绿色 - 5生命值
  BOSS: '#8E44AD', // 紫色 - Boss砖块
  SPECIAL: '#FFD700' // 金色 - 特效砖块
};

export default class Brick extends Animation {
  constructor() {
    super(BRICK_IMG_SRC, BRICK_WIDTH, BRICK_HEIGHT);

    // 砖块形状：正方形或长方形
    this.shape = Math.random() < 0.5 ? BRICK_SHAPES.SQUARE : BRICK_SHAPES.RECTANGLE;
    if (this.shape === BRICK_SHAPES.SQUARE) {
      this.width = BRICK_WIDTH;
      this.height = BRICK_WIDTH; // 正方形
    } else {
      this.width = BRICK_WIDTH * 1.5; // 长方形
      this.height = BRICK_HEIGHT;
    }

    this.maxHealth = 2; // 最大生命值
    this.health = 2; // 当前生命值
    this.type = BRICK_TYPES.NORMAL; // 砖块类型
    this.hitFlashTimer = 0; // 击打闪光计时器
    this.isFlashing = false; // 是否正在闪光
  }

  init(x, y, health = 2, type = BRICK_TYPES.NORMAL) {
    this.x = x;
    this.y = y;
    this.maxHealth = health;
    this.health = health;
    this.type = type;

    // 10%概率生成特效砖块
    if (Math.random() < 0.1 && type === BRICK_TYPES.NORMAL) {
      this.type = BRICK_TYPES.SPECIAL;
    }

    this.isActive = true;
    this.visible = true;
    this.hitFlashTimer = 0;
    this.isFlashing = false;

    // 设置爆炸动画
    this.initExplosionAnimation();
  }

  // 预定义爆炸的帧动画
  initExplosionAnimation() {
    const EXPLO_FRAME_COUNT = 19;
    const frames = Array.from(
      { length: EXPLO_FRAME_COUNT },
      (_, i) => `${EXPLO_IMG_PREFIX}${i + 1}.png`
    );
    this.initFrames(frames);
  }

  // 每一帧更新砖块状态
  update() {
    if (GameGlobal.databus.isGameOver) {
      return;
    }

    // 更新击打闪光效果
    if (this.isFlashing) {
      this.hitFlashTimer--;
      if (this.hitFlashTimer <= 0) {
        this.isFlashing = false;
      }
    }

    // 砖块向下移动（类似俄罗斯方块）
    // 根据关卡调整下降速度，增加紧张感
    const level = GameGlobal.databus.level;
    const dropInterval = Math.max(30, 90 - level * 8); // 更快的下降速度

    if (GameGlobal.databus.frame % dropInterval === 0) {
      this.y += BRICK_HEIGHT * 0.8; // 更大的移动距离
    }

    // 如果砖块到达底部，游戏结束
    if (this.y > SCREEN_HEIGHT - 120) {
      GameGlobal.databus.gameOver();
    }
  }

  // 被球击中
  hit() {
    this.health--;

    // 触发击打闪光效果
    this.isFlashing = true;
    this.hitFlashTimer = 10; // 闪光10帧

    // 创建击打粒子效果
    const color = this.type === BRICK_TYPES.BOSS ?
      BRICK_COLORS.BOSS : BRICK_COLORS[this.health + 1] || BRICK_COLORS[2];
    if (GameGlobal.particleSystem) {
      GameGlobal.particleSystem.createBrickHitEffect(
        this.x + this.width / 2,
        this.y + this.height / 2,
        color
      );
    }

    // 播放击打音效
    GameGlobal.musicManager.playShoot();

    // 轻微震动
    wx.vibrateShort({
      type: 'light'
    });

    // 增加分数
    GameGlobal.databus.score += 10;

    // 如果生命值归零，销毁砖块
    if (this.health <= 0) {
      // 特效砖块被击碎时触发能力
      if (this.type === BRICK_TYPES.SPECIAL) {
        this.triggerSpecialEffect();
      }
      this.destroy();
      return true; // 返回true表示砖块被销毁
    }

    return false; // 返回false表示砖块还存在
  }

  // 触发特效砖块的能力
  triggerSpecialEffect() {
    // 激活多球效果 - 短时间内发射速度加快
    if (GameGlobal.databus && GameGlobal.databus.launcher) {
      GameGlobal.databus.launcher.activateMultiBallEffect();
    }

    // 创建特殊粒子效果
    if (GameGlobal.particleSystem) {
      const centerX = this.x + this.width / 2;
      const centerY = this.y + this.height / 2;
      GameGlobal.particleSystem.createSpecialBrickEffect(centerX, centerY);
    }

    // 增加额外分数
    GameGlobal.databus.score += 50;
  }

  // 几何风格渲染方法
  render(ctx) {
    if (!this.visible) return;

    // 保存当前绘图状态
    ctx.save();

    // 如果正在闪光，设置闪光效果
    if (this.isFlashing) {
      ctx.globalAlpha = 0.7 + 0.3 * Math.sin(this.hitFlashTimer * 0.8);
      ctx.shadowColor = '#FFFFFF';
      ctx.shadowBlur = 15;
    }

    // 根据类型和生命值设置颜色
    let color;
    if (this.type === BRICK_TYPES.SPECIAL) {
      color = BRICK_COLORS.SPECIAL;
    } else if (this.type === BRICK_TYPES.BOSS) {
      color = BRICK_COLORS.BOSS;
    } else {
      color = BRICK_COLORS[this.health] || BRICK_COLORS[2];
    }

    // 几何线条绘制 - 只绘制边框，不填充
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.strokeRect(this.x, this.y, this.width, this.height);

    // 特效砖块添加额外装饰
    if (this.type === BRICK_TYPES.SPECIAL) {
      // 绘制内部十字线
      ctx.beginPath();
      ctx.moveTo(this.x + this.width / 2, this.y);
      ctx.lineTo(this.x + this.width / 2, this.y + this.height);
      ctx.moveTo(this.x, this.y + this.height / 2);
      ctx.lineTo(this.x + this.width, this.y + this.height / 2);
      ctx.stroke();

      // 绘制角落小方块
      const cornerSize = 4;
      ctx.fillStyle = color;
      ctx.fillRect(this.x, this.y, cornerSize, cornerSize);
      ctx.fillRect(this.x + this.width - cornerSize, this.y, cornerSize, cornerSize);
      ctx.fillRect(this.x, this.y + this.height - cornerSize, cornerSize, cornerSize);
      ctx.fillRect(this.x + this.width - cornerSize, this.y + this.height - cornerSize, cornerSize, cornerSize);
    }

    // 绘制生命值数字
    if (this.type !== BRICK_TYPES.SPECIAL) {
      ctx.fillStyle = color;
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        this.health.toString(),
        this.x + this.width / 2,
        this.y + this.height / 2 + 5
      );
    }

    // 恢复绘图状态
    ctx.restore();
  }

  destroy() {
    this.isActive = false;

    // 创建销毁爆炸粒子效果
    const color = this.type === BRICK_TYPES.BOSS ?
      BRICK_COLORS.BOSS : BRICK_COLORS[this.maxHealth] || BRICK_COLORS[2];
    if (GameGlobal.particleSystem) {
      GameGlobal.particleSystem.createBrickDestroyEffect(
        this.x + this.width / 2,
        this.y + this.height / 2,
        color
      );
    }

    // 播放销毁动画
    this.playAnimation();
    GameGlobal.musicManager.playExplosion();
    wx.vibrateShort({
      type: 'medium'
    });

    // 增加销毁分数
    GameGlobal.databus.score += this.maxHealth * 20;

    this.on('stopAnimation', () => this.remove.bind(this));
  }

  remove() {
    this.isActive = false;
    this.visible = false;
    GameGlobal.databus.removeBrick(this);
  }
}

// 导出砖块类型常量
export { BRICK_TYPES, BRICK_COLORS };
