# 微信小游戏 - 弹球游戏

这是一个基于微信小游戏平台开发的弹球游戏，结合了经典弹球游戏和俄罗斯方块的元素。

## 游戏特色

### 🎮 游戏玩法
- **弹球机制**: 触摸屏幕调整发射角度，松开发射球
- **砖块系统**: 砖块有2-5生命值，不同颜色代表不同生命值
- **俄罗斯方块式下降**: 砖块会像俄罗斯方块一样不断向下生成
- **关卡系统**: 每5关有一个Boss关卡，难度逐渐增加
- **击打效果**: 砖块被击中时有闪光效果和粒子特效

### ✨ 视觉效果
- **粒子系统**: 击打砖块、球反弹、砖块销毁都有粒子效果
- **关卡完成庆祝**: 关卡完成时有庆祝动画和烟花效果
- **动态颜色**: 砖块颜色根据生命值动态变化
- **流畅动画**: 爆炸动画和击打闪光效果

### 🎯 游戏目标
- 用球击破所有砖块
- 不让砖块到达屏幕底部
- 获得更高的分数
- 挑战更高的关卡

## 源码目录介绍

```
├── audio                                      // 音频资源
├── images                                     // 图片资源
├── js
│   ├── base
│   │   ├── animatoin.js                       // 帧动画的简易实现
│   │   ├── pool.js                            // 对象池的简易实现
│   │   └── sprite.js                          // 游戏基本元素精灵类
│   ├── libs
│   │   └── tinyemitter.js                     // 事件监听和触发
│   ├── npc
│   │   └── enemy.js                           // 敌机类
│   ├── player
│   │   ├── bullet.js                          // 子弹类
│   │   └── index.js                           // 玩家类
│   ├── runtime
│   │   ├── background.js                      // 背景类
│   │   ├── gameinfo.js                        // 用于展示分数和结算界面
│   │   └── music.js                           // 全局音效管理器
│   ├── databus.js                             // 管控游戏状态
│   ├── main.js                                // 游戏入口主函数
│   └── render.js                              // 基础渲染信息
├── .eslintrc.js                               // 代码规范
├── game.js                                    // 游戏逻辑主入口
├── game.json                                  // 游戏运行时配置
├── project.config.json                        // 项目配置
└── project.private.config.json                // 项目个人配置
```
