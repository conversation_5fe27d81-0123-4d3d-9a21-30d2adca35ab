import './render'; // 初始化Canvas
import Launcher from './player/index'; // 导入发射器类
import Brick from './npc/brick'; // 导入砖块类
import BackGround from './runtime/background'; // 导入背景类
import GameInfo from './runtime/gameinfo'; // 导入游戏UI类
import Music from './runtime/music'; // 导入音乐类
import DataBus from './databus'; // 导入数据类，用于管理游戏状态和数据
import BrickGenerator from './runtime/brickgenerator'; // 导入砖块生成器
import Physics from './runtime/physics'; // 导入物理引擎
import './runtime/particles'; // 导入粒子系统

const ctx = canvas.getContext('2d'); // 获取canvas的2D绘图上下文;

GameGlobal.databus = new DataBus(); // 全局数据管理，用于管理游戏状态和数据
GameGlobal.musicManager = new Music(); // 全局音乐管理实例

/**
 * 弹球游戏主函数
 */
export default class Main {
  aniId = 0; // 用于存储动画帧的ID
  bg = new BackGround(); // 创建背景
  launcher = new Launcher(); // 创建发射器
  gameInfo = new GameInfo(); // 创建游戏UI显示
  brickGenerator = new BrickGenerator(); // 创建砖块生成器
  physics = new Physics(); // 创建物理引擎

  constructor() {
    // 当开始游戏被点击时，重新开始游戏
    this.gameInfo.on('restart', this.start.bind(this));

    // 开始游戏
    this.start();
  }

  /**
   * 开始或重启游戏
   */
  start() {
    GameGlobal.databus.reset(); // 重置数据
    this.launcher.init(); // 重置发射器状态
    this.brickGenerator.reset(); // 重置砖块生成器
    this.physics.reset(); // 重置物理引擎

    // 生成初始砖块
    this.brickGenerator.generateInitialBricks();

    cancelAnimationFrame(this.aniId); // 清除上一局的动画
    this.aniId = requestAnimationFrame(this.loop.bind(this)); // 开始新的动画循环
  }

  /**
   * 检查关卡完成和进入下一关
   */
  checkLevelProgression() {
    if (GameGlobal.databus.checkLevelComplete()) {
      // 关卡完成，创建庆祝效果
      if (GameGlobal.particleSystem) {
        GameGlobal.particleSystem.createLevelCompleteEffect(
          canvas.width / 2,
          canvas.height / 2
        );

        // Boss关卡完成时创建烟花效果
        if (GameGlobal.databus.isBossLevel) {
          setTimeout(() => {
            GameGlobal.particleSystem.createFireworksEffect();
          }, 500);
        }
      }

      // 1秒后生成新关卡的砖块
      setTimeout(() => {
        this.brickGenerator.generateInitialBricks();
      }, 1000);
    }
  }

  /**
   * 全局碰撞检测
   */
  collisionDetection() {
    // 使用物理引擎处理碰撞
    this.physics.detectAndHandleCollisions();

    // 检查球是否掉出底部
    GameGlobal.databus.balls.forEach((ball) => {
      if (ball.y > canvas.height + ball.height) {
        ball.destroy();
        this.launcher.onBallDestroyed();
      }
    });

    // 检查是否所有球都消失且没有砖块被清除（游戏失败条件）
    if (GameGlobal.databus.balls.length === 0 && this.launcher.ballsInPlay === 0) {
      // 如果还有砖块存在，可能需要重新发射
      if (GameGlobal.databus.bricks.length > 0) {
        // 这里可以添加重新发射的逻辑，或者游戏结束
        // 暂时让玩家可以继续发射
        this.launcher.canShoot = true;
      }
    }
  }

  /**
   * canvas重绘函数
   * 每一帧重新绘制所有的需要展示的元素
   */
  render() {
    ctx.clearRect(0, 0, canvas.width, canvas.height); // 清空画布

    this.bg.render(ctx); // 绘制背景
    this.launcher.render(ctx); // 绘制发射器
    GameGlobal.databus.balls.forEach((item) => item.render(ctx)); // 绘制所有球
    GameGlobal.databus.bricks.forEach((item) => item.render(ctx)); // 绘制所有砖块

    // 绘制粒子效果
    if (GameGlobal.particleSystem) {
      GameGlobal.particleSystem.render(ctx);
    }

    this.gameInfo.render(ctx); // 绘制游戏UI
    GameGlobal.databus.animations.forEach((ani) => {
      if (ani.isPlaying) {
        ani.aniRender(ctx); // 渲染动画
      }
    }); // 绘制所有动画

    // 绘制关卡信息
    this.renderLevelInfo(ctx);
  }

  /**
   * 绘制关卡信息
   */
  renderLevelInfo(ctx) {
    ctx.save();
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '20px Arial';
    ctx.textAlign = 'left';

    // 显示关卡
    const levelText = GameGlobal.databus.isBossLevel ?
      `Boss Level ${GameGlobal.databus.level}` :
      `Level ${GameGlobal.databus.level}`;
    ctx.fillText(levelText, 20, 40);

    // 显示分数
    ctx.fillText(`Score: ${GameGlobal.databus.score}`, 20, 70);

    // 显示剩余砖块数
    ctx.fillText(`Bricks: ${GameGlobal.databus.bricks.length}`, 20, 100);

    ctx.restore();
  }

  // 游戏逻辑更新主函数
  update() {
    GameGlobal.databus.frame++; // 增加帧数

    if (GameGlobal.databus.isGameOver) {
      return;
    }

    this.bg.update(); // 更新背景
    this.launcher.update(); // 更新发射器
    this.brickGenerator.update(); // 更新砖块生成器

    // 更新粒子系统
    if (GameGlobal.particleSystem) {
      GameGlobal.particleSystem.update();
    }

    // 更新所有球
    GameGlobal.databus.balls.forEach((item) => item.update());
    // 更新所有砖块
    GameGlobal.databus.bricks.forEach((item) => item.update());

    this.collisionDetection(); // 检测碰撞
    this.checkLevelProgression(); // 检查关卡进度
  }

  // 实现游戏帧循环
  loop() {
    this.update(); // 更新游戏逻辑
    this.render(); // 渲染游戏画面

    // 请求下一帧动画
    this.aniId = requestAnimationFrame(this.loop.bind(this));
  }
}
