/**
 * 物理引擎类
 * 负责处理球与砖块、墙壁的碰撞检测和反弹计算
 */
export default class Physics {
  constructor() {
    // 物理常量
    this.gravity = 0; // 重力（弹球游戏通常不需要重力）
    this.friction = 0.99; // 摩擦力（轻微减速）
    this.bounceDamping = 0.95; // 反弹阻尼
  }

  /**
   * 检测球与砖块的碰撞
   * @param {Ball} ball - 球对象
   * @param {Brick} brick - 砖块对象
   * @returns {boolean} - 是否发生碰撞
   */
  checkBallBrickCollision(ball, brick) {
    if (!ball.isActive || !brick.isActive || !ball.visible || !brick.visible) {
      return false;
    }

    // 使用AABB碰撞检测
    const ballLeft = ball.x;
    const ballRight = ball.x + ball.width;
    const ballTop = ball.y;
    const ballBottom = ball.y + ball.height;

    const brickLeft = brick.x;
    const brickRight = brick.x + brick.width;
    const brickTop = brick.y;
    const brickBottom = brick.y + brick.height;

    return (
      ballLeft < brickRight &&
      ballRight > brickLeft &&
      ballTop < brickBottom &&
      ballBottom > brickTop
    );
  }

  /**
   * 处理球与砖块的碰撞响应
   * @param {Ball} ball - 球对象
   * @param {Brick} brick - 砖块对象
   */
  handleBallBrickCollision(ball, brick) {
    // 计算球心与砖块中心的相对位置
    const ballCenterX = ball.x + ball.width / 2;
    const ballCenterY = ball.y + ball.height / 2;
    const brickCenterX = brick.x + brick.width / 2;
    const brickCenterY = brick.y + brick.height / 2;

    const deltaX = ballCenterX - brickCenterX;
    const deltaY = ballCenterY - brickCenterY;

    // 计算重叠量
    const overlapX = (ball.width + brick.width) / 2 - Math.abs(deltaX);
    const overlapY = (ball.height + brick.height) / 2 - Math.abs(deltaY);

    // 根据重叠量较小的方向进行反弹
    if (overlapX < overlapY) {
      // 水平碰撞
      ball.velocityX = -ball.velocityX * this.bounceDamping;
      
      // 调整球的位置，避免卡在砖块内
      if (deltaX > 0) {
        ball.x = brick.x + brick.width + 1;
      } else {
        ball.x = brick.x - ball.width - 1;
      }
    } else {
      // 垂直碰撞
      ball.velocityY = -ball.velocityY * this.bounceDamping;
      
      // 调整球的位置，避免卡在砖块内
      if (deltaY > 0) {
        ball.y = brick.y + brick.height + 1;
      } else {
        ball.y = brick.y - ball.height - 1;
      }
    }

    // 添加一些随机性，让游戏更有趣
    const randomFactor = 0.1;
    ball.velocityX += (Math.random() - 0.5) * randomFactor;
    ball.velocityY += (Math.random() - 0.5) * randomFactor;

    // 确保球的速度不会太慢
    const minSpeed = 3;
    const currentSpeed = Math.sqrt(ball.velocityX * ball.velocityX + ball.velocityY * ball.velocityY);
    if (currentSpeed < minSpeed) {
      const speedMultiplier = minSpeed / currentSpeed;
      ball.velocityX *= speedMultiplier;
      ball.velocityY *= speedMultiplier;
    }

    // 限制最大速度
    const maxSpeed = 12;
    if (currentSpeed > maxSpeed) {
      const speedMultiplier = maxSpeed / currentSpeed;
      ball.velocityX *= speedMultiplier;
      ball.velocityY *= speedMultiplier;
    }
  }

  /**
   * 检测球与发射器的碰撞（可选功能，用于球回到发射器）
   * @param {Ball} ball - 球对象
   * @param {Launcher} launcher - 发射器对象
   * @returns {boolean} - 是否发生碰撞
   */
  checkBallLauncherCollision(ball, launcher) {
    if (!ball.isActive || !launcher.isActive || !ball.visible || !launcher.visible) {
      return false;
    }

    // 只有当球向下运动时才检测与发射器的碰撞
    if (ball.velocityY <= 0) {
      return false;
    }

    const ballLeft = ball.x;
    const ballRight = ball.x + ball.width;
    const ballTop = ball.y;
    const ballBottom = ball.y + ball.height;

    const launcherLeft = launcher.x;
    const launcherRight = launcher.x + launcher.width;
    const launcherTop = launcher.y;
    const launcherBottom = launcher.y + launcher.height;

    return (
      ballLeft < launcherRight &&
      ballRight > launcherLeft &&
      ballTop < launcherBottom &&
      ballBottom > launcherTop
    );
  }

  /**
   * 处理球与发射器的碰撞响应
   * @param {Ball} ball - 球对象
   * @param {Launcher} launcher - 发射器对象
   */
  handleBallLauncherCollision(ball, launcher) {
    // 计算球撞击发射器的位置
    const ballCenterX = ball.x + ball.width / 2;
    const launcherCenterX = launcher.x + launcher.width / 2;
    const hitPosition = (ballCenterX - launcherCenterX) / (launcher.width / 2);

    // 根据撞击位置调整反弹角度
    const maxAngle = Math.PI / 3; // 最大60度角
    const bounceAngle = hitPosition * maxAngle;

    // 计算新的速度
    const speed = Math.sqrt(ball.velocityX * ball.velocityX + ball.velocityY * ball.velocityY);
    ball.velocityX = Math.sin(bounceAngle) * speed;
    ball.velocityY = -Math.abs(Math.cos(bounceAngle) * speed); // 确保向上反弹

    // 调整球的位置，确保不会卡在发射器内
    ball.y = launcher.y - ball.height - 1;
  }

  /**
   * 更新球的物理状态
   * @param {Ball} ball - 球对象
   */
  updateBall(ball) {
    if (!ball.isActive) {
      return;
    }

    // 应用摩擦力
    ball.velocityX *= this.friction;
    ball.velocityY *= this.friction;

    // 应用重力（如果需要）
    ball.velocityY += this.gravity;

    // 更新位置
    ball.x += ball.velocityX;
    ball.y += ball.velocityY;
  }

  /**
   * 检测并处理所有碰撞
   */
  detectAndHandleCollisions() {
    const balls = GameGlobal.databus.balls;
    const bricks = GameGlobal.databus.bricks;

    // 检测球与砖块的碰撞
    balls.forEach(ball => {
      if (!ball.isActive) return;

      for (let i = bricks.length - 1; i >= 0; i--) {
        const brick = bricks[i];
        if (!brick.isActive) continue;

        if (this.checkBallBrickCollision(ball, brick)) {
          this.handleBallBrickCollision(ball, brick);
          
          // 砖块被击中
          const destroyed = brick.hit();
          if (destroyed) {
            // 砖块被销毁，检查是否过关
            if (GameGlobal.databus.checkLevelComplete()) {
              // 进入下一关的逻辑将在主游戏循环中处理
            }
          }
          
          break; // 一次只处理一个碰撞
        }
      }
    });
  }

  /**
   * 重置物理引擎
   */
  reset() {
    // 物理引擎通常不需要特殊的重置逻辑
  }
}
