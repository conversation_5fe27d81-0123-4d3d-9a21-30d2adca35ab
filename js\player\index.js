import Sprite from '../base/sprite';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render';
import Ball from './ball';

// 发射器相关常量设置
const LAUNCHER_IMG_SRC = 'images/hero.png';
const LAUNCHER_WIDTH = 80;
const LAUNCHER_HEIGHT = 40;

export default class Launcher extends Sprite {
  constructor() {
    super(LAUNCHER_IMG_SRC, LAUNCHER_WIDTH, LAUNCHER_HEIGHT);

    // 发射角度（弧度）
    this.angle = -Math.PI / 2; // 默认向上发射
    this.minAngle = -Math.PI + 0.2; // 最小角度（接近水平向左）
    this.maxAngle = -0.2; // 最大角度（接近水平向右）

    // 瞄准线相关
    this.aimLineLength = 100;
    this.showAimLine = false;

    // 发射相关
    this.canShoot = true;
    this.ballsInPlay = 0; // 场上球的数量
    this.maxBalls = 10; // 最大球数，允许多个球同时存在
    this.shootCooldown = 0; // 发射冷却时间
    this.shootCooldownTime = 10; // 发射冷却帧数（约0.17秒）

    // 自动发射相关
    this.autoShoot = true; // 自动发射开关
    this.autoShootInterval = 30; // 自动发射间隔（帧数）
    this.autoShootTimer = 0; // 自动发射计时器

    // 移动相关
    this.moveSpeed = 5; // 移动速度
    this.isMovingLeft = false;
    this.isMovingRight = false;

    // 特效相关
    this.multiBallEffect = false; // 多球特效状态
    this.multiBallTimer = 0; // 特效持续时间
    this.multiBallDuration = 300; // 特效持续帧数（5秒）

    // 初始化坐标
    this.init();

    // 初始化事件监听
    this.initEvent();
  }

  init() {
    // 发射器位于屏幕底部居中位置
    this.x = SCREEN_WIDTH / 2 - this.width / 2;
    this.y = SCREEN_HEIGHT - this.height - 20;

    this.isActive = true;
    this.visible = true;
    this.angle = -Math.PI / 2;
    this.showAimLine = false;
    this.canShoot = true;
    this.ballsInPlay = 0;
    this.shootCooldown = 0;
    this.autoShootTimer = 0;
    this.isMovingLeft = false;
    this.isMovingRight = false;
    this.multiBallEffect = false;
    this.multiBallTimer = 0;
  }

  /**
   * 初始化事件监听器
   * 监听用户的触摸事件来调整发射角度和移动
   */
  initEvent() {
    wx.onTouchStart((e) => {
      const { clientX: x, clientY: y } = e.touches[0];

      if (GameGlobal.databus.isGameOver) {
        return;
      }

      // 检查游戏状态
      if (GameGlobal.gameState) {
        if (GameGlobal.gameState.isState('paused')) {
          GameGlobal.gameState.setState('playing');
          return;
        } else if (!GameGlobal.gameState.isState('playing')) {
          return;
        }
      }

      // 检查是否点击在发射器左右两侧进行移动
      const launcherCenterX = this.x + this.width / 2;
      if (Math.abs(x - launcherCenterX) > this.width / 2) {
        // 点击在发射器外侧，进行移动
        if (x < launcherCenterX) {
          this.isMovingLeft = true;
        } else {
          this.isMovingRight = true;
        }
      } else {
        // 点击在发射器上，调整角度
        this.showAimLine = true;
        this.updateAimAngle(x, y);
      }
    });

    wx.onTouchMove((e) => {
      const { clientX: x, clientY: y } = e.touches[0];

      if (GameGlobal.databus.isGameOver) {
        return;
      }

      if (this.showAimLine) {
        this.updateAimAngle(x, y);
      }
    });

    wx.onTouchEnd(() => {
      if (GameGlobal.databus.isGameOver) {
        return;
      }

      // 停止移动
      this.isMovingLeft = false;
      this.isMovingRight = false;
      this.showAimLine = false;
    });

    wx.onTouchCancel(() => {
      this.showAimLine = false;
      this.isMovingLeft = false;
      this.isMovingRight = false;
    });
  }

  /**
   * 根据触摸位置更新瞄准角度
   */
  updateAimAngle(touchX, touchY) {
    // 计算发射器中心点
    const launcherCenterX = this.x + this.width / 2;
    const launcherCenterY = this.y + this.height / 2;

    // 计算角度
    const deltaX = touchX - launcherCenterX;
    const deltaY = touchY - launcherCenterY;
    let angle = Math.atan2(deltaY, deltaX);

    // 限制角度范围（只能向上半圆发射）
    angle = Math.max(this.minAngle, Math.min(this.maxAngle, angle));

    this.angle = angle;
  }

  /**
   * 发射球
   */
  shoot() {
    if (!this.canShoot || this.ballsInPlay >= this.maxBalls || this.shootCooldown > 0) {
      return;
    }

    const ball = GameGlobal.databus.pool.getItemByClass('ball', Ball);
    const launcherCenterX = this.x + this.width / 2;
    const launcherCenterY = this.y;

    ball.init(launcherCenterX - ball.width / 2, launcherCenterY - ball.height, this.angle);
    GameGlobal.databus.balls.push(ball);
    this.ballsInPlay++;

    // 设置发射冷却时间
    this.shootCooldown = this.shootCooldownTime;
    this.canShoot = false;

    GameGlobal.musicManager.playShoot(); // 播放发射音效
  }

  /**
   * 球被销毁时调用，减少场上球数
   */
  onBallDestroyed() {
    this.ballsInPlay--;
    if (this.ballsInPlay < 0) {
      this.ballsInPlay = 0;
    }
    // 只要球数没有达到上限且冷却时间结束，就允许发射
    if (this.ballsInPlay < this.maxBalls && this.shootCooldown <= 0) {
      this.canShoot = true;
    }
  }

  /**
   * 渲染发射器（白色三角形）和瞄准线
   */
  render(ctx) {
    // 保存绘图状态
    ctx.save();

    // 绘制白色三角形发射器
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    const size = this.width / 2;

    ctx.fillStyle = this.multiBallEffect ? '#FFD700' : '#FFFFFF'; // 特效时显示金色
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 2;

    // 绘制三角形
    ctx.beginPath();
    ctx.moveTo(centerX, this.y); // 顶点
    ctx.lineTo(centerX - size, this.y + this.height); // 左下角
    ctx.lineTo(centerX + size, this.y + this.height); // 右下角
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // 特效状态下添加光晕
    if (this.multiBallEffect) {
      ctx.shadowColor = '#FFD700';
      ctx.shadowBlur = 15;
      ctx.stroke();
    }

    ctx.restore();

    // 渲染瞄准线
    if (this.showAimLine) {
      this.renderAimLine(ctx);
    }
  }

  /**
   * 渲染瞄准线
   */
  renderAimLine(ctx) {
    const launcherCenterX = this.x + this.width / 2;
    const launcherCenterY = this.y + this.height / 2;

    const endX = launcherCenterX + Math.cos(this.angle) * this.aimLineLength;
    const endY = launcherCenterY + Math.sin(this.angle) * this.aimLineLength;

    ctx.save();
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 3;
    ctx.setLineDash([10, 5]); // 虚线效果
    ctx.beginPath();
    ctx.moveTo(launcherCenterX, launcherCenterY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
    ctx.restore();
  }

  update() {
    // 更新发射冷却时间
    if (this.shootCooldown > 0) {
      this.shootCooldown--;
      if (this.shootCooldown <= 0) {
        this.canShoot = true;
      }
    }

    // 处理左右移动
    if (this.isMovingLeft && this.x > 0) {
      this.x -= this.moveSpeed;
    }
    if (this.isMovingRight && this.x < SCREEN_WIDTH - this.width) {
      this.x += this.moveSpeed;
    }

    // 自动发射逻辑
    if (this.autoShoot) {
      this.autoShootTimer++;
      const shootInterval = this.multiBallEffect ?
        this.autoShootInterval / 3 : this.autoShootInterval; // 特效时发射更快

      if (this.autoShootTimer >= shootInterval && this.canShoot &&
          this.ballsInPlay < this.maxBalls && this.shootCooldown <= 0) {
        this.shoot();
        this.autoShootTimer = 0;
      }
    }

    // 更新多球特效
    if (this.multiBallEffect) {
      this.multiBallTimer++;
      if (this.multiBallTimer >= this.multiBallDuration) {
        this.multiBallEffect = false;
        this.multiBallTimer = 0;
      }
    }
  }

  /**
   * 激活多球特效
   */
  activateMultiBallEffect() {
    this.multiBallEffect = true;
    this.multiBallTimer = 0;

    // 创建特效提示
    if (GameGlobal.particleSystem) {
      GameGlobal.particleSystem.createPowerUpEffect(
        this.x + this.width / 2,
        this.y
      );
    }
  }
}
