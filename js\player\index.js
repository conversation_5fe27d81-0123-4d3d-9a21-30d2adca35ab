import Sprite from '../base/sprite';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render';
import Ball from './ball';

// 发射器相关常量设置
const LAUNCHER_IMG_SRC = 'images/hero.png';
const LAUNCHER_WIDTH = 80;
const LAUNCHER_HEIGHT = 40;

export default class Launcher extends Sprite {
  constructor() {
    super(LAUNCHER_IMG_SRC, LAUNCHER_WIDTH, LAUNCHER_HEIGHT);

    // 发射角度（弧度）
    this.angle = -Math.PI / 2; // 默认向上发射
    this.minAngle = -Math.PI + 0.2; // 最小角度（接近水平向左）
    this.maxAngle = -0.2; // 最大角度（接近水平向右）

    // 瞄准线相关
    this.aimLineLength = 100;
    this.showAimLine = false;

    // 发射相关
    this.canShoot = true;
    this.ballsInPlay = 0; // 场上球的数量
    this.maxBalls = 1; // 最大球数

    // 初始化坐标
    this.init();

    // 初始化事件监听
    this.initEvent();
  }

  init() {
    // 发射器位于屏幕底部居中位置
    this.x = SCREEN_WIDTH / 2 - this.width / 2;
    this.y = SCREEN_HEIGHT - this.height - 20;

    this.isActive = true;
    this.visible = true;
    this.angle = -Math.PI / 2;
    this.showAimLine = false;
    this.canShoot = true;
    this.ballsInPlay = 0;
  }

  /**
   * 初始化事件监听器
   * 监听用户的触摸事件来调整发射角度和发射球
   */
  initEvent() {
    wx.onTouchStart((e) => {
      const { clientX: x, clientY: y } = e.touches[0];

      if (GameGlobal.databus.isGameOver) {
        return;
      }

      this.showAimLine = true;
      this.updateAimAngle(x, y);
    });

    wx.onTouchMove((e) => {
      const { clientX: x, clientY: y } = e.touches[0];

      if (GameGlobal.databus.isGameOver) {
        return;
      }

      if (this.showAimLine) {
        this.updateAimAngle(x, y);
      }
    });

    wx.onTouchEnd((e) => {
      if (GameGlobal.databus.isGameOver) {
        return;
      }

      if (this.showAimLine && this.canShoot) {
        this.shoot();
      }
      this.showAimLine = false;
    });

    wx.onTouchCancel((e) => {
      this.showAimLine = false;
    });
  }

  /**
   * 根据触摸位置更新瞄准角度
   */
  updateAimAngle(touchX, touchY) {
    // 计算发射器中心点
    const launcherCenterX = this.x + this.width / 2;
    const launcherCenterY = this.y + this.height / 2;

    // 计算角度
    const deltaX = touchX - launcherCenterX;
    const deltaY = touchY - launcherCenterY;
    let angle = Math.atan2(deltaY, deltaX);

    // 限制角度范围（只能向上半圆发射）
    angle = Math.max(this.minAngle, Math.min(this.maxAngle, angle));

    this.angle = angle;
  }

  /**
   * 发射球
   */
  shoot() {
    if (!this.canShoot || this.ballsInPlay >= this.maxBalls) {
      return;
    }

    const ball = GameGlobal.databus.pool.getItemByClass('ball', Ball);
    const launcherCenterX = this.x + this.width / 2;
    const launcherCenterY = this.y;

    ball.init(launcherCenterX - ball.width / 2, launcherCenterY - ball.height, this.angle);
    GameGlobal.databus.balls.push(ball);
    this.ballsInPlay++;

    GameGlobal.musicManager.playShoot(); // 播放发射音效
  }

  /**
   * 球被销毁时调用，减少场上球数
   */
  onBallDestroyed() {
    this.ballsInPlay--;
    if (this.ballsInPlay < 0) {
      this.ballsInPlay = 0;
    }
  }

  /**
   * 渲染发射器和瞄准线
   */
  render(ctx) {
    // 渲染发射器本体
    super.render(ctx);

    // 渲染瞄准线
    if (this.showAimLine) {
      this.renderAimLine(ctx);
    }
  }

  /**
   * 渲染瞄准线
   */
  renderAimLine(ctx) {
    const launcherCenterX = this.x + this.width / 2;
    const launcherCenterY = this.y + this.height / 2;

    const endX = launcherCenterX + Math.cos(this.angle) * this.aimLineLength;
    const endY = launcherCenterY + Math.sin(this.angle) * this.aimLineLength;

    ctx.save();
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 3;
    ctx.setLineDash([10, 5]); // 虚线效果
    ctx.beginPath();
    ctx.moveTo(launcherCenterX, launcherCenterY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
    ctx.restore();
  }

  update() {
    // 发射器不需要特殊的更新逻辑
  }
}
