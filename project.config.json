{"description": "项目配置文件", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "newFeature": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": false}, "compileType": "game", "libVersion": "latest", "appid": "wxbc2fadbff17a3bdf", "projectname": "quickstart", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"currentL": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "isGameTourist": false, "editorSetting": {}}