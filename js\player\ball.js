import Sprite from '../base/sprite';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render';

const BALL_IMG_SRC = 'images/bullet.png'; // 暂时使用子弹图片作为球
const BALL_WIDTH = 20;
const BALL_HEIGHT = 20;

export default class Ball extends Sprite {
  constructor() {
    super(BALL_IMG_SRC, BALL_WIDTH, BALL_HEIGHT);
    
    // 物理属性
    this.velocityX = 0; // X方向速度
    this.velocityY = 0; // Y方向速度
    this.speed = 8; // 基础速度
    this.radius = BALL_WIDTH / 2; // 球的半径
  }

  init(x, y, angle) {
    this.x = x;
    this.y = y;
    
    // 根据角度计算初始速度
    this.velocityX = Math.cos(angle) * this.speed;
    this.velocityY = Math.sin(angle) * this.speed;
    
    this.isActive = true;
    this.visible = true;
  }

  // 每一帧更新球的位置
  update() {
    if (GameGlobal.databus.isGameOver) {
      return;
    }
    
    // 更新位置
    this.x += this.velocityX;
    this.y += this.velocityY;
    
    // 边界碰撞检测和反弹
    this.handleWallCollision();
    
    // 检查是否掉出底部
    if (this.y > SCREEN_HEIGHT + this.height) {
      this.destroy();
    }
  }

  // 处理与墙壁的碰撞
  handleWallCollision() {
    // 左右边界反弹
    if (this.x <= 0 || this.x + this.width >= SCREEN_WIDTH) {
      this.velocityX = -this.velocityX;
      // 确保球不会卡在边界外
      this.x = Math.max(0, Math.min(SCREEN_WIDTH - this.width, this.x));
    }
    
    // 上边界反弹
    if (this.y <= 0) {
      this.velocityY = -this.velocityY;
      this.y = 0;
    }
  }

  // 与砖块碰撞后的反弹
  bounceOffBrick(brick) {
    // 计算球心与砖块中心的相对位置
    const ballCenterX = this.x + this.width / 2;
    const ballCenterY = this.y + this.height / 2;
    const brickCenterX = brick.x + brick.width / 2;
    const brickCenterY = brick.y + brick.height / 2;

    const deltaX = ballCenterX - brickCenterX;
    const deltaY = ballCenterY - brickCenterY;

    // 判断碰撞面
    const overlapX = (this.width + brick.width) / 2 - Math.abs(deltaX);
    const overlapY = (this.height + brick.height) / 2 - Math.abs(deltaY);

    if (overlapX < overlapY) {
      // 水平碰撞
      this.velocityX = -this.velocityX;
    } else {
      // 垂直碰撞
      this.velocityY = -this.velocityY;
    }

    // 创建反弹粒子效果
    if (GameGlobal.particleSystem) {
      GameGlobal.particleSystem.createBallBounceEffect(ballCenterX, ballCenterY);
    }
  }

  // 获取球心坐标
  getCenterX() {
    return this.x + this.width / 2;
  }

  getCenterY() {
    return this.y + this.height / 2;
  }

  destroy() {
    this.isActive = false;
    this.remove();
  }

  remove() {
    this.isActive = false;
    this.visible = false;
    // 回收球对象
    GameGlobal.databus.removeBalls(this);
  }
}
