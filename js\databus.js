import Pool from './base/pool';

let instance;

/**
 * 全局状态管理器
 * 负责管理游戏的状态，包括帧数、分数、子弹、敌人和动画等
 */
export default class DataBus {
  // 直接在类中定义实例属性
  bricks = []; // 存储砖块
  balls = []; // 存储球
  animations = []; // 存储动画
  frame = 0; // 当前帧数
  score = 0; // 当前分数
  level = 1; // 当前关卡
  isGameOver = false; // 游戏是否结束
  isBossLevel = false; // 是否为Boss关卡
  pool = new Pool(); // 初始化对象池

  constructor() {
    // 确保单例模式
    if (instance) return instance;

    instance = this;
  }

  // 重置游戏状态
  reset() {
    this.frame = 0; // 当前帧数
    this.score = 0; // 当前分数
    this.level = 1; // 当前关卡
    this.balls = []; // 存储球
    this.bricks = []; // 存储砖块
    this.animations = []; // 存储动画
    this.isGameOver = false; // 游戏是否结束
    this.isBossLevel = false; // 是否为Boss关卡
  }

  // 游戏结束
  gameOver() {
    this.isGameOver = true;
  }

  /**
   * 回收砖块，进入对象池
   * 此后不进入帧循环
   * @param {Object} brick - 要回收的砖块对象
   */
  removeBrick(brick) {
    const temp = this.bricks.splice(this.bricks.indexOf(brick), 1);
    if (temp) {
      this.pool.recover('brick', brick); // 回收砖块到对象池
    }
  }

  /**
   * 回收球，进入对象池
   * 此后不进入帧循环
   * @param {Object} ball - 要回收的球对象
   */
  removeBalls(ball) {
    const temp = this.balls.splice(this.balls.indexOf(ball), 1);
    if (temp) {
      this.pool.recover('ball', ball); // 回收球到对象池
    }
  }

  /**
   * 检查是否需要进入下一关
   */
  checkLevelComplete() {
    if (this.bricks.length === 0) {
      this.level++;
      this.isBossLevel = (this.level % 5 === 0); // 每5关一个Boss关
      return true;
    }
    return false;
  }
}
