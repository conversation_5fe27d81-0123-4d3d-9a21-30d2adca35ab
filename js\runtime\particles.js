/**
 * 粒子类
 */
class Particle {
  constructor(x, y, velocityX, velocityY, color, life) {
    this.x = x;
    this.y = y;
    this.velocityX = velocityX;
    this.velocityY = velocityY;
    this.color = color;
    this.life = life;
    this.maxLife = life;
    this.size = Math.random() * 4 + 2;
    this.gravity = 0.1;
    this.friction = 0.98;
  }

  update() {
    this.x += this.velocityX;
    this.y += this.velocityY;
    this.velocityY += this.gravity;
    this.velocityX *= this.friction;
    this.velocityY *= this.friction;
    this.life--;
  }

  render(ctx) {
    const alpha = this.life / this.maxLife;
    ctx.save();
    ctx.globalAlpha = alpha;
    ctx.fillStyle = this.color;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
    ctx.fill();
    ctx.restore();
  }

  isDead() {
    return this.life <= 0;
  }
}

/**
 * 粒子系统类
 * 负责管理和渲染粒子效果
 */
export default class ParticleSystem {
  constructor() {
    this.particles = [];
  }

  /**
   * 创建砖块击打粒子效果
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {string} color - 粒子颜色
   */
  createBrickHitEffect(x, y, color) {
    const particleCount = 8;
    for (let i = 0; i < particleCount; i++) {
      const angle = (Math.PI * 2 * i) / particleCount;
      const speed = Math.random() * 3 + 2;
      const velocityX = Math.cos(angle) * speed;
      const velocityY = Math.sin(angle) * speed;
      const life = Math.random() * 20 + 20;
      
      this.particles.push(new Particle(x, y, velocityX, velocityY, color, life));
    }
  }

  /**
   * 创建砖块销毁爆炸效果
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {string} color - 粒子颜色
   */
  createBrickDestroyEffect(x, y, color) {
    const particleCount = 15;
    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const speed = Math.random() * 5 + 3;
      const velocityX = Math.cos(angle) * speed;
      const velocityY = Math.sin(angle) * speed;
      const life = Math.random() * 30 + 30;
      
      this.particles.push(new Particle(x, y, velocityX, velocityY, color, life));
    }
  }

  /**
   * 创建球反弹效果
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  createBallBounceEffect(x, y) {
    const particleCount = 5;
    const color = '#FFFFFF';
    
    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const speed = Math.random() * 2 + 1;
      const velocityX = Math.cos(angle) * speed;
      const velocityY = Math.sin(angle) * speed;
      const life = Math.random() * 15 + 10;
      
      this.particles.push(new Particle(x, y, velocityX, velocityY, color, life));
    }
  }

  /**
   * 创建关卡完成庆祝效果
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   */
  createLevelCompleteEffect(centerX, centerY) {
    const particleCount = 30;
    const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
    
    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const speed = Math.random() * 6 + 4;
      const velocityX = Math.cos(angle) * speed;
      const velocityY = Math.sin(angle) * speed;
      const life = Math.random() * 60 + 60;
      const color = colors[Math.floor(Math.random() * colors.length)];
      
      this.particles.push(new Particle(centerX, centerY, velocityX, velocityY, color, life));
    }
  }

  /**
   * 创建连续的烟花效果
   */
  createFireworksEffect() {
    const fireworkCount = 3;
    const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#F39C12'];
    
    for (let f = 0; f < fireworkCount; f++) {
      const centerX = Math.random() * canvas.width;
      const centerY = Math.random() * (canvas.height / 2) + 50;
      const particleCount = 20;
      const color = colors[Math.floor(Math.random() * colors.length)];
      
      for (let i = 0; i < particleCount; i++) {
        const angle = (Math.PI * 2 * i) / particleCount;
        const speed = Math.random() * 4 + 3;
        const velocityX = Math.cos(angle) * speed;
        const velocityY = Math.sin(angle) * speed;
        const life = Math.random() * 40 + 40;
        
        this.particles.push(new Particle(centerX, centerY, velocityX, velocityY, color, life));
      }
    }
  }

  /**
   * 更新所有粒子
   */
  update() {
    // 更新粒子
    this.particles.forEach(particle => particle.update());
    
    // 移除死亡的粒子
    this.particles = this.particles.filter(particle => !particle.isDead());
  }

  /**
   * 渲染所有粒子
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    this.particles.forEach(particle => particle.render(ctx));
  }

  /**
   * 清除所有粒子
   */
  clear() {
    this.particles = [];
  }

  /**
   * 创建特效砖块爆炸效果
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  createSpecialBrickEffect(x, y) {
    const particleCount = 20;
    const colors = ['#FFD700', '#FFA500', '#FFFF00', '#FF6347'];

    for (let i = 0; i < particleCount; i++) {
      const angle = (Math.PI * 2 * i) / particleCount;
      const speed = Math.random() * 8 + 4;
      const velocityX = Math.cos(angle) * speed;
      const velocityY = Math.sin(angle) * speed;
      const color = colors[Math.floor(Math.random() * colors.length)];
      const life = Math.random() * 30 + 30;

      this.particles.push(new Particle(x, y, velocityX, velocityY, color, life));
    }
  }

  /**
   * 创建能力激活效果
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  createPowerUpEffect(x, y) {
    const particleCount = 15;

    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const speed = Math.random() * 6 + 2;
      const velocityX = Math.cos(angle) * speed;
      const velocityY = Math.sin(angle) * speed - 2; // 向上偏移
      const color = '#FFD700';
      const life = Math.random() * 40 + 20;

      this.particles.push(new Particle(x, y, velocityX, velocityY, color, life));
    }
  }

  /**
   * 获取粒子数量
   * @returns {number} 当前粒子数量
   */
  getParticleCount() {
    return this.particles.length;
  }
}

// 创建全局粒子系统实例
GameGlobal.particleSystem = new ParticleSystem();
